"""
LangGraph Plan-and-Execute Agent - Advanced Example
包含可视化和更完整的功能
"""

import operator
import asyncio
from typing import Annotated, List, Tuple, TypedDict, Union

from langchain_core.messages import BaseMessage, HumanMessage
from langchain_core.prompts import ChatPromptTemplate
from pydantic import BaseModel, Field
from langchain_openai import ChatOpenAI
from langgraph.graph import END, StateGraph

# 配置模型
import os
os.environ['OPENAI_API_KEY'] = 'sk-b875074119af42c0973544a9ff84866f'
os.environ['OPENAI_API_BASE'] = 'https://api.deepseek.com/v1'

# 初始化模型
model = ChatOpenAI(
    model="deepseek-chat",
    temperature=0,
    api_key=os.environ['OPENAI_API_KEY'],
    base_url=os.environ['OPENAI_API_BASE']
)

# 模拟搜索工具
class AdvancedMockSearchTool:
    name = "search"
    description = "A comprehensive search engine for finding information"

    def __init__(self):
        self.knowledge_base = {
            # 体育相关
            "2024 australian open": "Jannik Sinner won the men's singles title at the 2024 Australian Open, defeating Daniil Medvedev in the final with scores of 3-6, 3-6, 6-4, 6-4, 6-3.",
            "jannik sinner": "Jannik Sinner is an Italian professional tennis player born on August 16, 2001, in Sexten, South Tyrol, Italy.",
            "sexten italy": "Sexten (Italian: Sesto) is a comune in South Tyrol in northern Italy, located in the Puster Valley.",

            # 天气相关
            "weather": "Today's weather is partly cloudy with a temperature of 22°C and light winds.",
            "temperature": "The current temperature is 22°C with humidity at 65%.",

            # AI相关
            "artificial intelligence": "Recent AI developments include advances in large language models, computer vision, and autonomous systems.",
            "machine learning": "Machine learning continues to evolve with new architectures and applications across various industries.",

            # 科技相关
            "technology news": "Latest technology news includes developments in quantum computing, renewable energy, and space exploration.",
            "programming": "Programming languages continue to evolve with new frameworks and tools for developers.",
        }

    def search(self, query: str) -> str:
        """执行搜索"""
        query_lower = query.lower()

        # 查找最匹配的结果
        best_match = None
        best_score = 0

        for key, value in self.knowledge_base.items():
            # 计算匹配度
            words_in_key = key.split()
            words_in_query = query_lower.split()

            matches = sum(1 for word in words_in_key if word in words_in_query)
            score = matches / len(words_in_key) if words_in_key else 0

            if score > best_score:
                best_score = score
                best_match = value

        if best_match and best_score > 0.3:
            return best_match
        else:
            return f"Search completed for '{query}'. General information available but no specific match found."

# 创建工具实例
search_tool = AdvancedMockSearchTool()

# 定义状态
class PlanExecuteState(TypedDict):
    input: str
    plan: List[str]
    past_steps: Annotated[List[Tuple[str, str]], operator.add]
    response: str

# 定义Pydantic模型
class Plan(BaseModel):
    """计划执行的步骤"""
    steps: List[str] = Field(
        description="按顺序执行的不同步骤列表"
    )

class Response(BaseModel):
    """对用户的最终响应"""
    response: str = Field(
        description="对用户查询的最终答案"
    )

class Act(BaseModel):
    """要执行的动作"""
    action: str = Field(
        description="要执行的动作类型，应该是 'search' 或 'finish' 之一"
    )

# 创建提示模板
planner_prompt = ChatPromptTemplate.from_messages([
    ("system",
     "对于给定的目标，制定一个简单的分步计划。"
     "这个计划应该包含单独的任务，如果正确执行将产生正确的答案。"
     "不要添加任何多余的步骤。"
     "最后一步的结果应该是最终答案。"
     "确保每个步骤都有所需的所有信息 - 不要跳过步骤。"),
    ("human", "{input}"),
])

replanner_prompt = ChatPromptTemplate.from_messages([
    ("system",
     "对于给定的目标，制定一个简单的分步计划。"
     "这个计划应该包含单独的任务，如果正确执行将产生正确的答案。"
     "不要添加任何多余的步骤。"
     "最后一步的结果应该是最终答案。"
     "确保每个步骤都有所需的所有信息 - 不要跳过步骤。\n\n"
     "你的目标是：{input}\n\n"
     "你的原始计划是：{plan}\n\n"
     "你目前已经完成的步骤：{past_steps}\n\n"
     "相应地更新你的计划。如果不需要更多步骤并且可以返回给用户，那么请回应。"
     "否则，填写计划。只添加仍然需要完成的步骤到计划中。不要将之前完成的步骤作为计划的一部分返回。"),
    ("human", "根据上述信息更新计划"),
])

# 创建规划器和重新规划器
planner = planner_prompt | model.with_structured_output(Plan)
replanner = replanner_prompt | model.with_structured_output(Union[Plan, Response])

# 节点函数
async def plan_step(state: PlanExecuteState):
    """创建初始计划"""
    plan = await planner.ainvoke({"input": state["input"]})
    return {"plan": plan.steps}

async def execute_step(state: PlanExecuteState):
    """执行计划中的下一步"""
    plan = state["plan"]
    if not plan:
        return {"response": "计划已完成，但没有找到具体答案。"}

    # 执行第一个步骤
    task = plan[0]
    print(f"正在执行步骤: {task}")

    # 使用搜索工具执行任务
    result = search_tool.search(task)

    return {
        "past_steps": [(task, result)],
    }

async def replan_step(state: PlanExecuteState):
    """重新规划或完成任务"""
    # 准备重新规划的输入
    plan_str = "\n".join(f"{i+1}. {step}" for i, step in enumerate(state["plan"]))
    past_steps_str = "\n".join(f"- {step}: {result}" for step, result in state["past_steps"])

    output = await replanner.ainvoke({
        "input": state["input"],
        "plan": plan_str,
        "past_steps": past_steps_str
    })

    if isinstance(output, Response):
        return {"response": output.response}
    else:
        return {"plan": output.steps}

def should_end(state: PlanExecuteState) -> str:
    """决定是否结束执行"""
    if "response" in state and state["response"]:
        return "end"
    else:
        return "continue"

# 创建工作流图
def create_plan_execute_graph():
    """创建计划执行图"""
    workflow = StateGraph(PlanExecuteState)

    # 添加节点
    workflow.add_node("planner", plan_step)
    workflow.add_node("agent", execute_step)
    workflow.add_node("replan", replan_step)

    # 设置入口点
    workflow.set_entry_point("planner")

    # 添加边
    workflow.add_edge("planner", "agent")
    workflow.add_edge("agent", "replan")
    workflow.add_conditional_edges(
        "replan",
        should_end,
        {"continue": "agent", "end": END}
    )

    return workflow.compile()

# 创建应用实例
app = create_plan_execute_graph()

# 主要运行函数
async def run_plan_execute_agent(query: str, verbose: bool = True):
    """运行计划执行代理"""
    config = {"recursion_limit": 50}
    inputs = {"input": query}

    if verbose:
        print(f"🤖 查询: {query}")
        print("=" * 60)

    final_result = None

    async for event in app.astream(inputs, config=config):
        for k, v in event.items():
            if k != "__end__":
                if verbose:
                    if k == "plan":
                        print(f"📋 计划: {v}")
                    elif k == "past_steps":
                        print(f"✅ 已完成步骤: {v}")
                    elif k == "response":
                        print(f"🎯 最终回答: {v}")
                        final_result = v
                    print()

    return final_result

# 可视化函数
def visualize_graph():
    """可视化工作流图"""
    try:
        from IPython.display import Image, display
        display(Image(app.get_graph(xray=True).draw_mermaid_png()))
    except ImportError:
        print("无法显示图形，需要安装 IPython 和相关依赖")
    except Exception as e:
        print(f"可视化图形时出错: {e}")

# 测试函数
async def test_plan_execute():
    """测试计划执行代理"""
    test_queries = [
        "2024年澳大利亚网球公开赛男单冠军的家乡是哪里？",
        "今天的天气怎么样？",
        "最近人工智能有什么新发展？"
    ]

    for i, query in enumerate(test_queries, 1):
        print(f"\n🧪 测试 {i}:")
        result = await run_plan_execute_agent(query)
        print(f"结果: {result}")
        print("=" * 80)

if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_plan_execute())
