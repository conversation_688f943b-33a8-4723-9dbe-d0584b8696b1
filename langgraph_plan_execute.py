"""
LangGraph Plan-and-Execute Agent Example
Based on the official LangGraph tutorial
"""

import operator
from typing import Annotated, List, Tuple, TypedDict

from langchain_core.messages import BaseMessage, HumanMessage
from langchain_core.prompts import ChatPromptTemplate
from pydantic import BaseModel, Field
from langchain_openai import ChatOpenAI
from langgraph.graph import END, StateGraph
from langgraph.prebuilt import ToolNode

# 配置模型 - 使用DeepSeek
import os
os.environ['OPENAI_API_KEY'] = 'sk-b875074119af42c0973544a9ff84866f'
os.environ['OPENAI_API_BASE'] = 'https://api.deepseek.com/v1'

# 初始化模型
model = ChatOpenAI(
    model="deepseek-chat",
    temperature=0,
    api_key=os.environ['OPENAI_API_KEY'],
    base_url=os.environ['OPENAI_API_BASE']
)

# 定义工具
from langchain_community.tools.tavily_search import TavilySearchResults

# 如果没有Tavily API key，我们创建一个模拟的搜索工具
class MockSearchTool:
    name = "tavily_search_results_json"
    description = "A search engine optimized for comprehensive, accurate, and trusted results."

    def __init__(self):
        pass

    def run(self, query: str) -> str:
        # 模拟搜索结果
        mock_results = {
            "2024 Australian Open": "Jannik Sinner won the men's singles title at the 2024 Australian Open, defeating Daniil Medvedev in the final.",
            "Jannik Sinner hometown": "Jannik Sinner is from Sexten, a town in northern Italy.",
            "weather": "Today's weather is sunny with a temperature of 22°C.",
            "current events": "Latest news includes various global events and developments."
        }

        # 简单的关键词匹配
        for key, value in mock_results.items():
            if any(word.lower() in query.lower() for word in key.split()):
                return value

        return f"Search results for '{query}': General information found."

# 创建工具实例
try:
    # 尝试使用真实的Tavily工具
    search_tool = TavilySearchResults(max_results=2)
except:
    # 如果失败，使用模拟工具
    search_tool = MockSearchTool()

tools = [search_tool]

# 定义状态
class PlanExecute(TypedDict):
    input: str
    plan: List[str]
    past_steps: Annotated[List[Tuple], operator.add]
    response: str

# 定义计划模式
class Plan(BaseModel):
    """Plan to follow in future"""
    steps: List[str] = Field(
        description="different steps to follow, should be in sorted order"
    )

# 定义响应模式
class Response(BaseModel):
    """Response to user."""
    response: str

# 定义重新计划模式
class Act(BaseModel):
    """Action to take."""
    action: str = Field(
        description="Action to take. Should be one of 'search' or 'finish'."
    )

# 创建规划器
planner_prompt = ChatPromptTemplate.from_messages([
    ("system",
     "For the given objective, come up with a simple step by step plan. "
     "This plan should involve individual tasks, that if executed correctly will yield the correct answer. "
     "Do not add any superfluous steps. "
     "The result of the final step should be the final answer. "
     "Make sure that each step has all the information needed - do not skip steps."),
    ("placeholder", "{messages}"),
])

planner = planner_prompt | model.with_structured_output(Plan)

# 创建重新规划器
replanner_prompt = ChatPromptTemplate.from_messages([
    ("system",
     "For the given objective, come up with a simple step by step plan. "
     "This plan should involve individual tasks, that if executed correctly will yield the correct answer. "
     "Do not add any superfluous steps. "
     "The result of the final step should be the final answer. "
     "Make sure that each step has all the information needed - do not skip steps.\n\n"
     "Your objective was this:\n{input}\n\n"
     "Your original plan was this:\n{plan}\n\n"
     "You have currently done the follow steps:\n{past_steps}\n\n"
     "Update your plan accordingly. If no more steps are needed and you can return to the user, then respond with that. "
     "Otherwise, fill out the plan. Only add steps to the plan that still NEED to be done. Do not return previously done steps as part of the plan."),
    ("placeholder", "{messages}"),
])

replanner = replanner_prompt | model.with_structured_output(Plan)

# 创建执行器
class ExecuteStep:
    def __init__(self, tools):
        self.tools = {tool.name if hasattr(tool, 'name') else 'search': tool for tool in tools}

    def execute(self, step: str) -> str:
        """执行单个步骤"""
        # 简单的执行逻辑 - 使用搜索工具
        if hasattr(search_tool, 'run'):
            return search_tool.run(step)
        else:
            # 对于TavilySearchResults
            return str(search_tool.invoke({"query": step}))

executor = ExecuteStep(tools)

# 定义节点函数
async def execute_step(state: PlanExecute):
    plan = state["plan"]
    plan_str = "\n".join(f"{i+1}. {step}" for i, step in enumerate(plan))
    task = plan[0]

    # 执行任务
    result = executor.execute(task)

    return {
        "past_steps": [(task, result)],
    }

async def plan_step(state: PlanExecute):
    plan = await planner.ainvoke({"messages": [HumanMessage(content=state["input"])]})
    return {"plan": plan.steps}

async def replan_step(state: PlanExecute):
    output = await replanner.ainvoke(state)
    if isinstance(output, Response):
        return {"response": output.response}
    else:
        return {"plan": output.steps}

def should_end(state: PlanExecute) -> str:
    if "response" in state and state["response"]:
        return "end"
    else:
        return "continue"

# 创建图
workflow = StateGraph(PlanExecute)

# 添加节点
workflow.add_node("planner", plan_step)
workflow.add_node("agent", execute_step)
workflow.add_node("replan", replan_step)

# 设置入口点
workflow.set_entry_point("planner")

# 添加边
workflow.add_edge("planner", "agent")
workflow.add_edge("agent", "replan")
workflow.add_conditional_edges(
    "replan",
    should_end,
    {"continue": "agent", "end": END}
)

# 编译图
app = workflow.compile()

# 测试函数
async def run_plan_execute(query: str):
    """运行计划执行代理"""
    config = {"recursion_limit": 50}
    inputs = {"input": query}

    print(f"Query: {query}\n")

    async for event in app.astream(inputs, config=config):
        for k, v in event.items():
            if k != "__end__":
                print(f"{k}: {v}")
                print()

if __name__ == "__main__":
    import asyncio

    # 测试查询
    test_queries = [
        "what is the hometown of the mens 2024 Australia open winner?",
        "What is the weather like today?",
        "Tell me about recent developments in AI"
    ]

    for query in test_queries:
        print("="*50)
        asyncio.run(run_plan_execute(query))
        print("="*50)
        print()
