# LangGraph 计划执行代理示例

基于 [LangGraph 官方教程](https://github.com/langchain-ai/langgraph/blob/main/docs/docs/tutorials/plan-and-execute/plan-and-execute.ipynb) 的计划执行代理实现。

## 📋 项目概述

这个项目实现了一个智能的计划执行代理，能够：
- 🧠 **智能规划**: 将复杂查询分解为可执行的步骤
- 🔍 **工具集成**: 使用搜索工具获取信息
- 🔄 **动态调整**: 根据执行结果调整计划
- 🎯 **目标导向**: 专注于获得准确的最终答案

## 🏗️ 架构设计

### 核心组件

1. **Planner (规划器)**
   - 接收用户查询
   - 生成分步执行计划
   - 使用 LLM 进行智能规划

2. **Agent (执行器)**
   - 执行计划中的单个步骤
   - 调用搜索工具获取信息
   - 记录执行结果

3. **Replanner (重新规划器)**
   - 评估已完成的步骤
   - 决定是否需要更多步骤
   - 生成最终回答或更新计划

4. **条件判断**
   - 检查是否完成所有必要步骤
   - 决定继续执行还是结束流程

### 工作流程

```
用户查询 → 规划器 → 执行器 → 重新规划器 → 条件判断
                ↑                    ↓
                └─── 继续执行 ←────────┘
                          ↓
                      最终回答
```

## 📁 文件结构

```
├── langgraph_plan_execute.py          # 基础实现
├── langgraph_plan_execute_advanced.py # 高级实现（推荐）
├── demo_plan_execute.py               # 演示脚本
└── README_langgraph.md               # 说明文档
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install langgraph langchain langchain-openai langchain-community
```

### 2. 配置环境

代码中已经配置了 DeepSeek API：
- API Key: `***********************************`
- Base URL: `https://api.deepseek.com/v1`

### 3. 运行演示

```bash
python demo_plan_execute.py
```

### 4. 直接使用

```python
import asyncio
from langgraph_plan_execute_advanced import run_plan_execute_agent

# 运行查询
result = asyncio.run(run_plan_execute_agent("你的查询"))
print(result)
```

## 💡 使用示例

### 示例 1: 体育查询
```
查询: "2024年澳大利亚网球公开赛男单冠军的家乡是哪里？"

计划:
1. 查找2024年澳大利亚网球公开赛男单冠军
2. 查找该冠军的家乡信息

执行结果: "Jannik Sinner的家乡是意大利北部的Sexten"
```

### 示例 2: 天气查询
```
查询: "今天的天气怎么样？"

计划:
1. 获取当前天气信息

执行结果: "今天天气部分多云，温度22°C，微风"
```

## 🔧 自定义配置

### 1. 更换 LLM 模型

```python
# 在 langgraph_plan_execute_advanced.py 中修改
model = ChatOpenAI(
    model="your-model-name",
    temperature=0,
    api_key="your-api-key",
    base_url="your-base-url"
)
```

### 2. 添加新工具

```python
# 创建新工具
class YourCustomTool:
    name = "your_tool"
    description = "工具描述"
    
    def search(self, query: str) -> str:
        # 实现你的工具逻辑
        return "工具执行结果"

# 在执行器中使用
executor = ExecuteStep([your_custom_tool])
```

### 3. 扩展知识库

```python
# 在 AdvancedMockSearchTool 中添加更多知识
self.knowledge_base = {
    "新主题": "相关信息",
    # ... 更多条目
}
```

## 🎮 演示模式

运行 `demo_plan_execute.py` 提供三种模式：

1. **交互式演示**: 实时输入查询并查看执行过程
2. **批量演示**: 运行预设的查询序列
3. **架构说明**: 查看详细的架构说明

## 🔍 核心特性

### 智能规划
- 自动分解复杂查询
- 生成逻辑清晰的执行步骤
- 避免冗余和无关步骤

### 动态执行
- 逐步执行计划
- 实时调整策略
- 记录执行历史

### 工具集成
- 支持多种工具类型
- 模块化工具设计
- 易于扩展新功能

### 中文支持
- 完整的中文界面
- 中文查询处理
- 本地化提示模板

## 🛠️ 技术栈

- **LangGraph**: 工作流编排
- **LangChain**: LLM 集成和工具管理
- **DeepSeek**: 大语言模型
- **Python**: 主要编程语言
- **AsyncIO**: 异步执行支持

## 📈 扩展建议

1. **并行执行**: 支持独立步骤的并行处理
2. **工具生态**: 集成更多外部工具和API
3. **持久化**: 添加对话历史和状态保存
4. **可视化**: 实时显示执行流程图
5. **监控**: 添加性能监控和日志记录

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

## 📄 许可证

本项目基于 MIT 许可证开源。

## 🔗 相关链接

- [LangGraph 官方文档](https://langchain-ai.github.io/langgraph/)
- [LangChain 文档](https://python.langchain.com/)
- [DeepSeek API](https://platform.deepseek.com/)

---

**注意**: 这是一个学习和演示项目，生产环境使用请根据实际需求进行适当修改和优化。
