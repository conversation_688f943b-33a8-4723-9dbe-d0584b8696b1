# Python 编译文件
__pycache__
*.pyc
*.pyo
*.pyd
*.pyc~
*.db

# 虚拟环境
.venv/
venv/
env/
ENV/

# IDE 配置文件
.idea/
.vscode/
*.swp
*.swo

# 日志与缓存
*.log
.cache/
logs/
tmp/

# 操作系统生成文件
.DS_Store
Thumbs.db

# 忽略本地配置文件
.env
.env.local
.local
.config
.env.development
.env.production

# 忽略 JAR 构建产物（可选）
*.jar
target/

# 忽略 node_modules（如果你有前端部分）
node_modules/

# 忽略运行时产生的 PID、lock 文件
*.pid
*.lock

# 忽略测试输出
htmlcov/
.coverage
.pytest_cache/
