# LangGraph Plan-and-Execute Agent Dependencies

# Core LangGraph and <PERSON><PERSON><PERSON><PERSON>
langgraph>=0.0.40
langchain>=0.1.0
langchain-core>=0.1.0
langchain-openai>=0.0.8
langchain-community>=0.0.20

# Optional: For real search functionality
# tavily-python>=0.3.0

# Async support
asyncio

# Data handling
pydantic>=1.10.0

# Optional: For visualization (if using Jupyter)
# ipython>=8.0.0
# graphviz>=0.20.0

# Optional: For enhanced functionality
# numpy>=1.24.0
# pandas>=2.0.0
