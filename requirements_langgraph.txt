# LangGraph Plan-and-Execute Agent Dependencies

# Core LangGraph and <PERSON><PERSON><PERSON><PERSON> (兼容版本)
langgraph>=0.0.40
langchain>=0.1.0
langchain-core>=0.1.0
langchain-openai>=0.0.8
langchain-community>=0.0.20

# Pydantic (确保版本兼容性)
pydantic>=2.0.0,<3.0.0

# Optional: For real search functionality
# tavily-python>=0.3.0

# Async support (Python内置)
# asyncio

# Optional: For visualization (if using Jupyter)
# ipython>=8.0.0
# graphviz>=0.20.0

# Optional: For enhanced functionality
# numpy>=1.24.0
# pandas>=2.0.0

# 推荐的稳定版本组合
# langgraph==0.0.55
# langchain==0.1.20
# langchain-core==0.1.52
# langchain-openai==0.1.8
# pydantic==2.5.0
