#!/usr/bin/env python3
"""
测试必要的包是否已安装
"""

def test_imports():
    """测试导入"""
    required_packages = [
        'langgraph',
        'langchain',
        'langchain_core',
        'langchain_openai',
        'asyncio',
        'typing',
        'operator'
    ]
    
    missing_packages = []
    available_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            available_packages.append(package)
            print(f"✅ {package} - 可用")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} - 未安装")
    
    print(f"\n📊 总结:")
    print(f"可用包: {len(available_packages)}")
    print(f"缺失包: {len(missing_packages)}")
    
    if missing_packages:
        print(f"\n🔧 需要安装的包:")
        print("pip install " + " ".join(missing_packages))
        return False
    else:
        print("\n🎉 所有必要的包都已安装!")
        return True

if __name__ == "__main__":
    test_imports()
