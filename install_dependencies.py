#!/usr/bin/env python3
"""
安装LangGraph计划执行代理所需的依赖包
"""

import subprocess
import sys
import os

def install_package(package):
    """安装单个包"""
    try:
        print(f"🔧 正在安装 {package}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package
        ], capture_output=True, text=True, check=True)
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package} 安装失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def main():
    """主安装函数"""
    print("🚀 LangGraph 计划执行代理 - 依赖安装")
    print("=" * 50)
    
    # 必需的包
    required_packages = [
        "langgraph>=0.0.40",
        "langchain>=0.1.0", 
        "langchain-core>=0.1.0",
        "langchain-openai>=0.0.8",
        "langchain-community>=0.0.20",
        "pydantic>=1.10.0"
    ]
    
    # 可选的包
    optional_packages = [
        "tavily-python>=0.3.0",  # 真实搜索功能
        "ipython>=8.0.0",        # Jupyter支持
        "graphviz>=0.20.0"       # 图形可视化
    ]
    
    print(f"📦 将安装 {len(required_packages)} 个必需包")
    print(f"📦 将安装 {len(optional_packages)} 个可选包")
    print()
    
    # 安装必需包
    print("🔧 安装必需包...")
    success_count = 0
    for package in required_packages:
        if install_package(package):
            success_count += 1
        print()
    
    print(f"📊 必需包安装结果: {success_count}/{len(required_packages)}")
    
    # 询问是否安装可选包
    if success_count == len(required_packages):
        print("\n🎯 所有必需包安装成功!")
        
        install_optional = input("\n是否安装可选包? (y/n): ").lower().strip()
        if install_optional in ['y', 'yes', '是']:
            print("\n🔧 安装可选包...")
            optional_success = 0
            for package in optional_packages:
                if install_package(package):
                    optional_success += 1
                print()
            print(f"📊 可选包安装结果: {optional_success}/{len(optional_packages)}")
    
    print("\n" + "=" * 50)
    print("🎉 安装完成!")
    print("\n📝 下一步:")
    print("1. 运行 'python test_imports.py' 验证安装")
    print("2. 运行 'python demo_plan_execute.py' 开始演示")
    print("\n💡 如果遇到问题，请检查网络连接和Python环境")

if __name__ == "__main__":
    main()
