#!/usr/bin/env python3
"""
测试修复版本的LangGraph计划执行代理
"""

import asyncio
import sys

async def test_fixed_version():
    """测试修复版本"""
    try:
        # 导入修复版本
        from langgraph_plan_execute_fixed import run_plan_execute_agent
        
        print("🧪 测试修复版本的LangGraph计划执行代理")
        print("=" * 60)
        
        # 测试查询
        test_query = "2024年澳大利亚网球公开赛男单冠军的家乡是哪里？"
        
        print(f"🔍 测试查询: {test_query}")
        print("-" * 60)
        
        # 运行代理
        result = await run_plan_execute_agent(test_query)
        
        print(f"\n🎉 测试成功!")
        print(f"最终结果: {result}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装必要的依赖包:")
        print("pip install langgraph langchain langchain-openai pydantic")
        return False
        
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_simple_version():
    """测试简化版本作为备选"""
    try:
        from simple_plan_execute import SimplePlanExecuteAgent
        
        print("\n🔄 使用简化版本进行测试")
        print("=" * 60)
        
        agent = SimplePlanExecuteAgent()
        test_query = "2024年澳大利亚网球公开赛男单冠军的家乡是哪里？"
        
        print(f"🔍 测试查询: {test_query}")
        print("-" * 60)
        
        result = agent.execute(test_query)
        
        print(f"\n🎉 简化版本测试成功!")
        print(f"最终结果: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 简化版本测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("🚀 LangGraph 计划执行代理测试")
    print("=" * 50)
    
    # 首先尝试修复版本
    success = await test_fixed_version()
    
    if not success:
        print("\n🔄 修复版本测试失败，尝试简化版本...")
        success = await test_simple_version()
    
    if success:
        print("\n✅ 测试完成，代理工作正常!")
    else:
        print("\n❌ 所有版本测试都失败了")
        print("\n💡 建议:")
        print("1. 检查Python环境和依赖包")
        print("2. 确保网络连接正常")
        print("3. 查看详细错误信息")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试程序出错: {e}")
        sys.exit(1)
