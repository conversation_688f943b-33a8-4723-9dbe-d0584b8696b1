import os
import warnings

from google.adk import Agent
from google.adk.models.lite_llm import LiteLlm

from gen_pic.config import MODEL_DEEPSEEK, SYSTEM_TEMPLATE

# root_agent = Agent(
#     name="weather_agent_v1",
#     model=MODEL_DEEPSEEK,  # 指定底层 LLM
#     description="为特定城市提供天气信息。",  # 对于稍后的任务分配至关重要
#     instruction="你是一个有用的天气助手和文件助手。你的主要目标是提供当前天气报告。帮助用户访问他们的文件系统"
#                 "当用户询问特定城市的天气时，"
#                 "你必须使用 'get_weather_stateful' 工具查找信息。"
#                 "分析工具的响应：如果状态为 'error'，礼貌地告知用户错误信息。"
#                 "如果状态为 'success'，向用户清晰简洁地呈现天气 'report'。"
#                 "仅在提到城市进行天气请求时使用该工具。",
#     tools=[],  # 使该工具可用于此智能体
# )
root_agent = Agent(
    name="genify",
    model=MODEL_DEEPSEEK,  # 指定底层 LLM
    description="生图助手",  # 对于稍后的任务分配至关重要
    instruction=SYSTEM_TEMPLATE,
    tools=[],  # 使该工具可用于此智能体
)
print(f"Agent '{root_agent.name}' created using model '{MODEL_DEEPSEEK}'.")
