#!/usr/bin/env python3
"""
测试简化版计划执行代理
"""

from simple_plan_execute import SimplePlanExecuteAgent

def test_agent():
    """测试代理功能"""
    print("🧪 测试简化版计划执行代理")
    print("=" * 50)
    
    agent = SimplePlanExecuteAgent()
    
    # 测试查询
    test_queries = [
        "2024年澳大利亚网球公开赛男单冠军的家乡是哪里？",
        "今天的天气怎么样？",
        "人工智能最近有什么发展？"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n🔍 测试 {i}: {query}")
        print("-" * 60)
        
        try:
            result = agent.execute(query, verbose=True)
            print(f"\n✅ 测试 {i} 完成")
        except Exception as e:
            print(f"\n❌ 测试 {i} 失败: {e}")
        
        print("\n" + "=" * 80)
        
        if i < len(test_queries):
            print("按回车键继续...")
            input()

if __name__ == "__main__":
    test_agent()
