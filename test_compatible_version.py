#!/usr/bin/env python3
"""
测试兼容版本的LangGraph计划执行代理
"""

import asyncio
import sys

async def test_compatible_version():
    """测试兼容版本"""
    try:
        print("🧪 测试兼容版本的LangGraph计划执行代理")
        print("=" * 60)
        print("此版本完全兼容DeepSeek API，不使用structured_output")
        print("=" * 60)
        
        # 导入兼容版本
        from langgraph_plan_execute_compatible import run_plan_execute_agent
        
        # 测试查询
        test_query = "2024年澳大利亚网球公开赛男单冠军的家乡是哪里？"
        
        print(f"🔍 测试查询: {test_query}")
        print("-" * 60)
        
        # 运行代理
        result = await run_plan_execute_agent(test_query)
        
        print(f"\n🎉 测试成功!")
        print(f"最终结果: {result}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装必要的依赖包:")
        print("pip install langgraph langchain langchain-openai")
        return False
        
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_multiple_queries():
    """测试多个查询"""
    try:
        from langgraph_plan_execute_compatible import run_plan_execute_agent
        
        print("\n🔄 测试多个查询")
        print("=" * 60)
        
        test_queries = [
            "2024年澳大利亚网球公开赛男单冠军的家乡是哪里？",
            "今天的天气怎么样？",
            "人工智能最近有什么发展？"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n📋 测试 {i}: {query}")
            print("-" * 50)
            
            result = await run_plan_execute_agent(query, verbose=False)
            print(f"✅ 结果: {result}")
            
            if i < len(test_queries):
                print("\n" + "⏳" * 20)
                await asyncio.sleep(1)  # 短暂延迟
        
        return True
        
    except Exception as e:
        print(f"❌ 多查询测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("🚀 LangGraph 兼容版本测试")
    print("=" * 50)
    print("解决了DeepSeek API的structured_output兼容性问题")
    print("=" * 50)
    
    # 测试单个查询
    success1 = await test_compatible_version()
    
    if success1:
        # 测试多个查询
        success2 = await test_multiple_queries()
        
        if success2:
            print("\n✅ 所有测试都成功完成!")
            print("\n💡 使用建议:")
            print("- 兼容版本适用于DeepSeek等不支持structured_output的API")
            print("- 使用JSON解析替代structured_output功能")
            print("- 包含完整的错误处理和回退机制")
        else:
            print("\n⚠️ 部分测试失败")
    else:
        print("\n❌ 基础测试失败")
        print("\n🔧 故障排除:")
        print("1. 检查网络连接")
        print("2. 验证API密钥")
        print("3. 确认依赖包已正确安装")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试程序出错: {e}")
        sys.exit(1)
