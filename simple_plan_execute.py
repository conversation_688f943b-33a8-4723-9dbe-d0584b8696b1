#!/usr/bin/env python3
"""
简化版计划执行代理
不依赖LangGraph，展示核心概念
"""

import json
import re
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

class ActionType(Enum):
    SEARCH = "search"
    FINISH = "finish"

@dataclass
class Step:
    """执行步骤"""
    description: str
    completed: bool = False
    result: Optional[str] = None

@dataclass
class Plan:
    """执行计划"""
    steps: List[Step]
    current_step: int = 0

class MockLLM:
    """模拟的大语言模型"""
    
    def __init__(self):
        self.planning_templates = {
            "澳大利亚网球": [
                "查找2024年澳大利亚网球公开赛男单冠军",
                "查找该冠军的个人信息和家乡"
            ],
            "天气": [
                "获取当前天气信息"
            ],
            "人工智能": [
                "搜索最新的人工智能发展动态",
                "总结主要的技术突破"
            ]
        }
    
    def create_plan(self, query: str) -> List[str]:
        """创建执行计划"""
        query_lower = query.lower()
        
        # 简单的关键词匹配
        if any(word in query_lower for word in ["澳大利亚", "网球", "冠军"]):
            return self.planning_templates["澳大利亚网球"]
        elif any(word in query_lower for word in ["天气", "气温"]):
            return self.planning_templates["天气"]
        elif any(word in query_lower for word in ["人工智能", "ai", "机器学习"]):
            return self.planning_templates["人工智能"]
        else:
            return [f"搜索关于'{query}'的信息"]
    
    def should_continue(self, completed_steps: List[Tuple[str, str]], original_query: str) -> bool:
        """判断是否需要继续执行"""
        # 简单逻辑：如果已完成的步骤数量达到预期，则停止
        if len(completed_steps) >= 2:
            return False
        
        # 检查是否已经获得了足够的信息
        results = " ".join([result for _, result in completed_steps])
        if "家乡" in original_query and "家乡" in results:
            return False
        if "天气" in original_query and "温度" in results:
            return False
            
        return True
    
    def generate_final_answer(self, completed_steps: List[Tuple[str, str]], original_query: str) -> str:
        """生成最终答案"""
        if not completed_steps:
            return "抱歉，无法找到相关信息。"
        
        # 合并所有步骤的结果
        all_results = []
        for step_desc, result in completed_steps:
            all_results.append(f"步骤：{step_desc}\n结果：{result}")
        
        combined_info = "\n\n".join(all_results)
        
        # 根据查询类型生成答案
        if "家乡" in original_query:
            return f"根据搜索结果，{combined_info.split('家乡')[0] if '家乡' in combined_info else '相关信息'}。"
        elif "天气" in original_query:
            return f"当前天气情况：{combined_info}"
        else:
            return f"查询结果：{combined_info}"

class MockSearchEngine:
    """模拟搜索引擎"""
    
    def __init__(self):
        self.knowledge_base = {
            "2024年澳大利亚网球公开赛男单冠军": "Jannik Sinner赢得了2024年澳大利亚网球公开赛男单冠军，他在决赛中击败了Daniil Medvedev。",
            "jannik sinner": "Jannik Sinner是意大利职业网球运动员，出生于2001年8月16日，家乡是意大利北部的Sexten。",
            "sexten": "Sexten（意大利语：Sesto）是意大利南蒂罗尔的一个市镇，位于意大利北部的普斯特谷。",
            "天气": "今天天气部分多云，温度22°C，湿度65%，微风。",
            "人工智能": "最新的人工智能发展包括大语言模型的进步、计算机视觉技术的突破以及自动驾驶系统的改进。",
            "机器学习": "机器学习领域持续发展，新的架构和算法在各个行业中得到应用。"
        }
    
    def search(self, query: str) -> str:
        """执行搜索"""
        query_lower = query.lower()
        
        # 查找最匹配的结果
        best_match = None
        best_score = 0
        
        for key, value in self.knowledge_base.items():
            # 计算匹配度
            key_words = key.lower().split()
            query_words = query_lower.split()
            
            matches = sum(1 for word in key_words if any(qw in word or word in qw for qw in query_words))
            score = matches / len(key_words) if key_words else 0
            
            if score > best_score:
                best_score = score
                best_match = value
        
        if best_match and best_score > 0.2:
            return best_match
        else:
            return f"搜索'{query}'完成，但未找到具体匹配的信息。"

class SimplePlanExecuteAgent:
    """简化版计划执行代理"""
    
    def __init__(self):
        self.llm = MockLLM()
        self.search_engine = MockSearchEngine()
        self.max_steps = 5
    
    def execute(self, query: str, verbose: bool = True) -> str:
        """执行查询"""
        if verbose:
            print(f"🤖 查询: {query}")
            print("=" * 60)
        
        # 1. 创建计划
        step_descriptions = self.llm.create_plan(query)
        plan = Plan(steps=[Step(desc) for desc in step_descriptions])
        
        if verbose:
            print(f"📋 计划:")
            for i, step in enumerate(plan.steps, 1):
                print(f"  {i}. {step.description}")
            print()
        
        # 2. 执行计划
        completed_steps = []
        
        for step_idx, step in enumerate(plan.steps):
            if step_idx >= self.max_steps:
                break
                
            if verbose:
                print(f"🔍 执行步骤 {step_idx + 1}: {step.description}")
            
            # 执行搜索
            result = self.search_engine.search(step.description)
            step.result = result
            step.completed = True
            completed_steps.append((step.description, result))
            
            if verbose:
                print(f"✅ 结果: {result}")
                print()
            
            # 3. 判断是否继续
            if not self.llm.should_continue(completed_steps, query):
                if verbose:
                    print("🎯 已获得足够信息，准备生成最终答案")
                break
        
        # 4. 生成最终答案
        final_answer = self.llm.generate_final_answer(completed_steps, query)
        
        if verbose:
            print(f"🎉 最终答案: {final_answer}")
        
        return final_answer

def demo():
    """演示函数"""
    print("🚀 简化版计划执行代理演示")
    print("=" * 50)
    print("这是一个不依赖外部库的简化实现")
    print("展示了计划执行代理的核心概念")
    print("=" * 50)
    
    agent = SimplePlanExecuteAgent()
    
    # 测试查询
    test_queries = [
        "2024年澳大利亚网球公开赛男单冠军的家乡是哪里？",
        "今天的天气怎么样？",
        "人工智能最近有什么发展？"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n🧪 测试 {i}:")
        print("-" * 40)
        result = agent.execute(query)
        print("\n" + "🔄" * 20)
        
        if i < len(test_queries):
            input("\n按回车键继续下一个测试...")

def interactive_demo():
    """交互式演示"""
    print("🎮 交互式计划执行代理")
    print("=" * 30)
    
    agent = SimplePlanExecuteAgent()
    
    while True:
        print("\n请选择:")
        print("1. 输入自定义查询")
        print("2. 运行预设演示")
        print("3. 退出")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == "1":
            query = input("\n请输入您的查询: ").strip()
            if query:
                print("\n" + "=" * 60)
                agent.execute(query)
            else:
                print("❌ 查询不能为空")
        
        elif choice == "2":
            demo()
        
        elif choice == "3":
            print("\n👋 再见!")
            break
        
        else:
            print("❌ 无效选择，请输入 1、2 或 3")

if __name__ == "__main__":
    try:
        interactive_demo()
    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断，再见!")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()
