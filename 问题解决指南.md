# LangGraph 计划执行代理 - 问题解决指南

## 🚨 常见问题及解决方案

### 1. Pydantic 版本兼容性问题

**错误信息:**
```
LangChainDeprecationWarning: As of langchain-core 0.3.0, Lang<PERSON><PERSON><PERSON> uses pydantic v2 internally. The langchain_core.pydantic_v1 module was a compatibility shim for pydantic v1, and should no longer be used.
```

**解决方案:**
- ✅ 使用修复版本: `langgraph_plan_execute_fixed.py`
- ✅ 更新导入语句: `from pydantic import BaseModel, Field`
- ✅ 确保Pydantic版本 >= 2.0.0

### 2. Union类型不支持问题

**错误信息:**
```
TypeError: typing.Union[__main__.Plan, __main__.Response] is not a module, class, method, or function.
```

**解决方案:**
- ✅ 避免使用Union类型在structured_output中
- ✅ 创建分离的规划器: `replanner_plan` 和 `replanner_response`
- ✅ 使用条件逻辑决定调用哪个规划器

### 3. 依赖包安装问题

**问题:** 缺少必要的包

**解决方案:**
```bash
# 方法1: 使用自动安装脚本
python install_dependencies.py

# 方法2: 手动安装
pip install langgraph langchain langchain-openai pydantic

# 方法3: 使用requirements文件
pip install -r requirements_langgraph.txt
```

### 4. API连接问题

**问题:** DeepSeek API连接失败

**解决方案:**
1. 检查网络连接
2. 验证API密钥是否正确
3. 确认API基础URL设置正确
4. 使用简化版本作为备选方案

## 📁 文件使用优先级

### 推荐使用顺序:

1. **`langgraph_plan_execute_fixed.py`** - 修复版本 (推荐)
   - 解决了所有已知兼容性问题
   - 包含错误处理和回退机制
   - 适合生产环境使用

2. **`simple_plan_execute.py`** - 简化版本 (备选)
   - 无外部依赖
   - 纯Python实现
   - 适合学习和理解核心概念

3. **`langgraph_plan_execute_advanced.py`** - 高级版本 (需要调试)
   - 功能最完整
   - 可能存在兼容性问题
   - 需要特定版本的依赖包

## 🧪 测试步骤

### 1. 快速测试
```bash
python test_fixed_version.py
```

### 2. 详细测试
```bash
# 测试修复版本
python langgraph_plan_execute_fixed.py

# 测试简化版本
python simple_plan_execute.py
```

### 3. 依赖检查
```bash
python test_imports.py
```

## 🔧 环境配置

### Python版本要求
- Python >= 3.8
- 推荐 Python 3.11+

### 虚拟环境设置
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境 (Windows)
venv\Scripts\activate

# 激活虚拟环境 (Linux/Mac)
source venv/bin/activate

# 安装依赖
pip install -r requirements_langgraph.txt
```

## 🐛 调试技巧

### 1. 启用详细日志
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 2. 检查模型响应
```python
# 在代码中添加调试输出
print(f"模型输出: {output}")
print(f"状态: {state}")
```

### 3. 使用try-catch包装
```python
try:
    result = await some_function()
except Exception as e:
    print(f"错误: {e}")
    # 提供备选方案
```

## 📞 获取帮助

### 如果问题仍然存在:

1. **检查错误日志** - 查看完整的错误堆栈
2. **验证环境** - 确保Python版本和依赖包正确
3. **使用简化版本** - 作为临时解决方案
4. **查看官方文档** - LangGraph和LangChain最新文档
5. **社区支持** - GitHub Issues和Discord社区

## 🎯 成功标志

当您看到以下输出时，说明代理工作正常:

```
🤖 查询: 2024年澳大利亚网球公开赛男单冠军的家乡是哪里？
============================================================
📋 计划: ['查找2024年澳大利亚网球公开赛男单冠军', '查找冠军的家乡信息']
🔍 正在执行: 查找2024年澳大利亚网球公开赛男单冠军
✅ 结果: Jannik Sinner赢得了2024年澳大利亚网球公开赛男单冠军...
🔍 正在执行: 查找冠军的家乡信息
✅ 结果: Jannik Sinner是意大利职业网球运动员，家乡是意大利北部的Sexten
🎯 最终回答: 根据搜索结果，2024年澳大利亚网球公开赛男单冠军Jannik Sinner的家乡是意大利北部的Sexten。
```

## 💡 最佳实践

1. **始终使用虚拟环境**
2. **定期更新依赖包**
3. **保留工作版本的备份**
4. **测试新版本前先备份**
5. **使用版本控制管理代码**
