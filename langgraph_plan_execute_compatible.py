"""
LangGraph Plan-and-Execute Agent - 兼容版本
完全兼容DeepSeek API，不使用structured_output
"""

import operator
import asyncio
import json
import re
from typing import Annotated, List, Tuple, TypedDict

from langchain_core.messages import HumanMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
from langgraph.graph import END, StateGraph

# 配置模型
import os
os.environ['OPENAI_API_KEY'] = 'sk-b875074119af42c0973544a9ff84866f'
os.environ['OPENAI_API_BASE'] = 'https://api.deepseek.com/v1'

# 初始化模型
model = ChatOpenAI(
    model="deepseek-chat",
    temperature=0,
    api_key=os.environ['OPENAI_API_KEY'],
    base_url=os.environ['OPENAI_API_BASE']
)

# 模拟搜索工具
class SearchTool:
    def __init__(self):
        self.knowledge_base = {
            "2024年澳大利亚网球公开赛男单冠军": "Jannik Sinner赢得了2024年澳大利亚网球公开赛男单冠军，在决赛中击败了Daniil Medvedev。",
            "jannik sinner": "Jannik Sinner是意大利职业网球运动员，出生于2001年8月16日，家乡是意大利北部的Sexten。",
            "sexten": "Sexten是意大利南蒂罗尔的一个市镇，位于意大利北部。",
            "天气": "今天天气部分多云，温度22°C，湿度65%。",
            "人工智能": "最新的AI发展包括大语言模型、计算机视觉和自动驾驶技术的进步。"
        }

    def search(self, query: str) -> str:
        query_lower = query.lower()
        for key, value in self.knowledge_base.items():
            if any(word in query_lower for word in key.lower().split()):
                return value
        return f"搜索'{query}'完成，找到一般性信息。"

search_tool = SearchTool()

# 定义状态
class PlanExecuteState(TypedDict):
    input: str
    plan: List[str]
    past_steps: Annotated[List[Tuple[str, str]], operator.add]
    response: str

# 创建提示模板
planner_prompt = ChatPromptTemplate.from_messages([
    ("system",
     "对于给定的目标，制定一个简单的分步计划。"
     "请以JSON格式返回计划，格式如下：\n"
     '{{"steps": ["步骤1", "步骤2", ...]}}\n'
     "这个计划应该包含单独的任务，如果正确执行将产生正确的答案。"
     "不要添加任何多余的步骤。"
     "最后一步的结果应该是最终答案。"),
    ("human", "{input}"),
])

response_prompt = ChatPromptTemplate.from_messages([
    ("system",
     "基于已完成的步骤，生成最终回答。"
     "请以JSON格式返回回答，格式如下：\n"
     '{{"response": "你的最终回答"}}\n'
     "你的目标是：{input}\n"
     "已完成的步骤：{past_steps}"),
    ("human", "请生成最终回答"),
])

# 解析JSON响应的辅助函数
def parse_json_response(text: str, default_key: str = "steps"):
    """解析模型返回的JSON响应"""
    try:
        # 尝试直接解析JSON
        if text.strip().startswith('{'):
            return json.loads(text.strip())

        # 查找JSON块
        json_match = re.search(r'\{[^}]*\}', text, re.DOTALL)
        if json_match:
            return json.loads(json_match.group())

        # 如果没有找到JSON，尝试提取列表
        if default_key == "steps":
            # 查找步骤列表
            steps = []
            lines = text.split('\n')
            for line in lines:
                line = line.strip()
                if line and (line.startswith('-') or line.startswith('•') or
                           re.match(r'^\d+\.', line)):
                    # 清理步骤文本
                    step = re.sub(r'^[-•\d\.]\s*', '', line).strip()
                    if step:
                        steps.append(step)

            if steps:
                return {"steps": steps}

        # 默认返回
        if default_key == "steps":
            return {"steps": [f"搜索关于'{text[:50]}'的信息"]}
        else:
            return {"response": text.strip()}

    except Exception as e:
        print(f"解析JSON失败: {e}")
        if default_key == "steps":
            return {"steps": ["执行搜索任务"]}
        else:
            return {"response": "基于搜索结果生成回答"}

# 节点函数
async def plan_step(state: PlanExecuteState):
    """创建初始计划"""
    try:
        response = await planner_prompt.ainvoke({"input": state["input"]})
        plan_text = await model.ainvoke(response.messages)

        # 解析计划
        plan_data = parse_json_response(plan_text.content, "steps")
        steps = plan_data.get("steps", [f"搜索关于'{state['input']}'的信息"])

        print(f"📋 生成计划: {steps}")
        return {"plan": steps}

    except Exception as e:
        print(f"规划出错: {e}")
        # 提供默认计划
        if "澳大利亚" in state["input"] or "网球" in state["input"]:
            return {"plan": ["查找2024年澳大利亚网球公开赛男单冠军", "查找冠军的家乡信息"]}
        elif "天气" in state["input"]:
            return {"plan": ["获取当前天气信息"]}
        else:
            return {"plan": [f"搜索关于'{state['input']}'的信息"]}

async def execute_step(state: PlanExecuteState):
    """执行计划中的下一步"""
    plan = state["plan"]
    if not plan:
        return {"response": "计划已完成。"}

    # 执行第一个步骤
    task = plan[0]
    print(f"🔍 正在执行: {task}")

    # 使用搜索工具执行任务
    result = search_tool.search(task)
    print(f"✅ 结果: {result}")

    return {
        "past_steps": [(task, result)],
    }

async def replan_step(state: PlanExecuteState):
    """重新规划或完成任务"""
    past_steps = state.get("past_steps", [])

    # 如果已经执行了足够的步骤，生成最终回答
    if len(past_steps) >= 2:
        try:
            past_steps_str = "\n".join(f"- {step}: {result}" for step, result in past_steps)

            response = await response_prompt.ainvoke({
                "input": state["input"],
                "past_steps": past_steps_str
            })
            response_text = await model.ainvoke(response.messages)

            # 解析响应
            response_data = parse_json_response(response_text.content, "response")
            final_response = response_data.get("response", "")

            if not final_response:
                # 手动生成回答
                combined_info = " ".join([result for _, result in past_steps])
                if "家乡" in state["input"]:
                    if "sexten" in combined_info.lower() or "意大利" in combined_info:
                        final_response = "根据搜索结果，2024年澳大利亚网球公开赛男单冠军Jannik Sinner的家乡是意大利北部的Sexten。"
                    else:
                        final_response = f"根据搜索结果：{combined_info}"
                elif "天气" in state["input"]:
                    final_response = f"当前天气情况：{combined_info}"
                else:
                    final_response = f"根据搜索结果：{combined_info}"

            print(f"🎯 生成最终回答: {final_response}")
            return {"response": final_response}

        except Exception as e:
            print(f"生成回答出错: {e}")
            # 手动生成回答
            combined_info = " ".join([result for _, result in past_steps])
            return {"response": f"基于搜索结果：{combined_info}"}

    # 继续执行剩余计划
    remaining_plan = state["plan"][1:] if len(state["plan"]) > 1 else []
    if remaining_plan:
        print(f"📋 更新计划: {remaining_plan}")
        return {"plan": remaining_plan}
    else:
        # 没有更多步骤，基于已有信息生成回答
        if past_steps:
            combined_info = " ".join([result for _, result in past_steps])
            return {"response": f"基于已完成的搜索：{combined_info}"}
        else:
            return {"response": "未找到相关信息。"}

def should_end(state: PlanExecuteState) -> str:
    """决定是否结束执行"""
    if "response" in state and state["response"]:
        return "end"
    else:
        return "continue"

# 创建工作流图
def create_plan_execute_graph():
    """创建计划执行图"""
    workflow = StateGraph(PlanExecuteState)

    # 添加节点
    workflow.add_node("planner", plan_step)
    workflow.add_node("agent", execute_step)
    workflow.add_node("replan", replan_step)

    # 设置入口点
    workflow.set_entry_point("planner")

    # 添加边
    workflow.add_edge("planner", "agent")
    workflow.add_edge("agent", "replan")
    workflow.add_conditional_edges(
        "replan",
        should_end,
        {"continue": "agent", "end": END}
    )

    return workflow.compile()

# 创建应用实例
app = create_plan_execute_graph()

# 主要运行函数
async def run_plan_execute_agent(query: str, verbose: bool = True):
    """运行计划执行代理"""
    config = {"recursion_limit": 50}
    inputs = {"input": query}

    if verbose:
        print(f"🤖 查询: {query}")
        print("=" * 60)

    final_result = None

    try:
        async for event in app.astream(inputs, config=config):
            for k, v in event.items():
                if k != "__end__":
                    if verbose:
                        if k == "plan":
                            print(f"📋 计划: {v}")
                        elif k == "past_steps":
                            print(f"✅ 已完成步骤: {v}")
                        elif k == "response":
                            print(f"🎯 最终回答: {v}")
                            final_result = v
                        print()
    except Exception as e:
        print(f"执行出错: {e}")
        final_result = "抱歉，执行过程中出现错误。"

    return final_result

# 测试函数
async def test_plan_execute():
    """测试计划执行代理"""
    test_queries = [
        # "2024年澳大利亚网球公开赛男单冠军的家乡是哪里？",
        # "今天的天气怎么样？",
        "最近人工智能有什么新发展？"
    ]

    for i, query in enumerate(test_queries, 1):
        print(f"\n🧪 测试 {i}:")
        result = await run_plan_execute_agent(query)
        print(f"结果: {result}")
        print("=" * 80)

if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_plan_execute())
