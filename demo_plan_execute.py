"""
LangGraph Plan-and-Execute Agent 演示脚本
简化版本，易于理解和运行
"""

import asyncio
from langgraph_plan_execute_advanced import run_plan_execute_agent, app

def print_banner():
    """打印横幅"""
    print("🚀 LangGraph 计划执行代理演示")
    print("=" * 50)
    print("基于官方教程的实现")
    print("支持中文查询和多步骤推理")
    print("=" * 50)

async def interactive_demo():
    """交互式演示"""
    print_banner()
    
    # 预设的示例查询
    example_queries = [
        "2024年澳大利亚网球公开赛男单冠军的家乡是哪里？",
        "今天的天气怎么样？",
        "人工智能最近有什么发展？",
        "意大利北部有哪些著名的城市？"
    ]
    
    print("\n📝 示例查询:")
    for i, query in enumerate(example_queries, 1):
        print(f"{i}. {query}")
    
    print("\n" + "=" * 50)
    
    while True:
        print("\n请选择:")
        print("1. 运行示例查询")
        print("2. 输入自定义查询")
        print("3. 退出")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == "1":
            print("\n选择示例查询:")
            for i, query in enumerate(example_queries, 1):
                print(f"{i}. {query}")
            
            try:
                query_choice = int(input("\n请输入查询编号: ").strip()) - 1
                if 0 <= query_choice < len(example_queries):
                    query = example_queries[query_choice]
                    print(f"\n🔍 执行查询: {query}")
                    print("-" * 50)
                    result = await run_plan_execute_agent(query)
                    print(f"\n✨ 最终结果: {result}")
                else:
                    print("❌ 无效的查询编号")
            except ValueError:
                print("❌ 请输入有效的数字")
        
        elif choice == "2":
            query = input("\n请输入您的查询: ").strip()
            if query:
                print(f"\n🔍 执行查询: {query}")
                print("-" * 50)
                result = await run_plan_execute_agent(query)
                print(f"\n✨ 最终结果: {result}")
            else:
                print("❌ 查询不能为空")
        
        elif choice == "3":
            print("\n👋 再见!")
            break
        
        else:
            print("❌ 无效选择，请输入 1、2 或 3")

async def batch_demo():
    """批量演示"""
    print_banner()
    print("\n🔄 批量演示模式")
    print("-" * 30)
    
    queries = [
        "2024年澳大利亚网球公开赛男单冠军是谁？",
        "Jannik Sinner的家乡在哪里？",
        "意大利北部的Sexten是什么地方？"
    ]
    
    for i, query in enumerate(queries, 1):
        print(f"\n📋 查询 {i}: {query}")
        print("=" * 60)
        result = await run_plan_execute_agent(query, verbose=True)
        print(f"🎯 结果: {result}")
        print("\n" + "🔄" * 20)

def show_architecture():
    """显示架构说明"""
    print("\n🏗️  LangGraph 计划执行代理架构")
    print("=" * 50)
    print("""
    工作流程:
    1. 📋 Planner (规划器)
       - 接收用户查询
       - 生成分步执行计划
    
    2. 🤖 Agent (执行器)
       - 执行计划中的单个步骤
       - 使用搜索工具获取信息
    
    3. 🔄 Replanner (重新规划器)
       - 评估已完成的步骤
       - 决定是否需要更多步骤
       - 生成最终回答或更新计划
    
    4. ✅ 条件判断
       - 检查是否完成所有必要步骤
       - 决定继续执行还是结束
    
    特点:
    - 🧠 智能规划: 自动分解复杂查询
    - 🔍 工具集成: 支持搜索和其他工具
    - 🔄 动态调整: 根据执行结果调整计划
    - 🎯 目标导向: 专注于获得准确答案
    """)

async def main():
    """主函数"""
    print("🎮 LangGraph 计划执行代理")
    print("=" * 30)
    print("1. 交互式演示")
    print("2. 批量演示")
    print("3. 查看架构说明")
    print("4. 退出")
    
    choice = input("\n请选择模式 (1-4): ").strip()
    
    if choice == "1":
        await interactive_demo()
    elif choice == "2":
        await batch_demo()
    elif choice == "3":
        show_architecture()
        input("\n按回车键继续...")
        await main()
    elif choice == "4":
        print("\n👋 再见!")
    else:
        print("❌ 无效选择")
        await main()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断，再见!")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
